#!/usr/bin/env node

/**
 * 测试冲突窗口自动显示问题修复效果
 * 验证登录后不再自动显示"解决冲突"窗口
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试冲突窗口自动显示问题修复效果...\n');

let allTestsPassed = true;

// 测试函数
function checkFileExists(filePath, description) {
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description}: ${filePath}`);
    return true;
  } else {
    console.log(`❌ ${description}: ${filePath} - 文件不存在`);
    return false;
  }
}

function checkFileContent(filePath, pattern, description) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    if (pattern.test(content)) {
      console.log(`✅ ${description}`);
      return true;
    } else {
      console.log(`❌ ${description} - 未找到预期内容`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${description} - 读取文件失败: ${error.message}`);
    return false;
  }
}

console.log('1. 验证测试冲突自动创建已禁用...');

// 检查testFixes.ts是否已禁用自动创建冲突
if (!checkFileContent(
  'editor/src/utils/testFixes.ts',
  /\/\/ 完全禁用自动运行测试和自动创建冲突数据/,
  '自动创建测试冲突已禁用'
)) {
  allTestsPassed = false;
}

// 检查是否移除了自动运行代码（现在是完全移除，不是注释）
if (!checkFileContent(
  'editor/src/utils/testFixes.ts',
  /\/\/ 所有测试功能仅供手动调用/,
  '自动运行测试代码已移除，仅供手动调用'
)) {
  allTestsPassed = false;
}

console.log('\n2. 验证ConflictResolutionService默认禁用...');

// 检查ConflictResolutionService默认状态
if (!checkFileContent(
  'editor/src/services/ConflictResolutionService.ts',
  /private enabled: boolean = false;/,
  'ConflictResolutionService默认禁用'
)) {
  allTestsPassed = false;
}

// 检查初始化方法
if (!checkFileContent(
  'editor/src/services/ConflictResolutionService.ts',
  /public setEnabled\(enabled: boolean\): void/,
  'ConflictResolutionService setEnabled方法存在'
)) {
  allTestsPassed = false;
}

console.log('\n3. 验证CollaborationPanel不自动启用冲突解决...');

// 检查CollaborationPanel是否移除了自动启用代码
if (!checkFileContent(
  'editor/src/components/collaboration/CollaborationPanel.tsx',
  /\/\/ 注意：不在这里自动启用冲突解决服务/,
  'CollaborationPanel不自动启用冲突解决服务'
)) {
  allTestsPassed = false;
}

console.log('\n4. 验证App.tsx微服务初始化延迟...');

// 检查App.tsx是否添加了延迟初始化
if (!checkFileContent(
  'editor/src/App.tsx',
  /\/\/ 延迟初始化，避免在登录后立即触发/,
  'App.tsx微服务延迟初始化'
)) {
  allTestsPassed = false;
}

// 检查是否添加了清理冲突状态的代码
if (!checkFileContent(
  'editor/src/App.tsx',
  /dispatch\(clearConflicts\(\)\);/,
  'App.tsx清理冲突状态'
)) {
  allTestsPassed = false;
}

console.log('\n5. 验证路径检查逻辑...');

// 检查App.tsx是否添加了路径检查
if (!checkFileContent(
  'editor/src/App.tsx',
  /const isEditorPage = currentPath\.includes\('\/editor\/'\)/,
  'App.tsx路径检查逻辑'
)) {
  allTestsPassed = false;
}

// 检查是否使用了shouldShowConflictPanel
if (!checkFileContent(
  'editor/src/App.tsx',
  /shouldShowConflictPanel &&/,
  'App.tsx使用shouldShowConflictPanel'
)) {
  allTestsPassed = false;
}

console.log('\n6. 验证配置文件一致性...');

// 检查.env文件
if (checkFileExists('.env', '.env配置文件')) {
  if (!checkFileContent(
    '.env',
    /REACT_APP_API_URL=http:\/\/localhost:3000\/api/,
    '.env API URL配置'
  )) {
    allTestsPassed = false;
  }
}

// 检查docker-compose.windows.yml
if (checkFileExists('docker-compose.windows.yml', 'Docker Compose配置文件')) {
  if (!checkFileContent(
    'docker-compose.windows.yml',
    /REACT_APP_API_URL=\/api/,
    'Docker Compose API URL配置'
  )) {
    allTestsPassed = false;
  }
}

// 检查启动脚本
if (!checkFileExists('start-windows.ps1', 'Windows启动脚本')) {
  allTestsPassed = false;
}

if (!checkFileExists('stop-windows.ps1', 'Windows停止脚本')) {
  allTestsPassed = false;
}

console.log('\n📊 修复总结:');
console.log('1. ✅ 禁用了testFixes.ts中的自动测试冲突创建');
console.log('2. ✅ 修改ConflictResolutionService默认为禁用状态');
console.log('3. ✅ 移除CollaborationPanel中的自动启用冲突解决服务');
console.log('4. ✅ 在App.tsx中添加微服务初始化延迟');
console.log('5. ✅ 在App.tsx中添加冲突状态清理');
console.log('6. ✅ 添加路径检查，只在编辑器页面显示冲突面板');
console.log('7. ✅ 验证配置文件一致性');

console.log('\n🎯 用户操作指南:');
console.log('1. 重新启动前端服务以应用修复:');
console.log('   Windows: .\\start-windows.ps1');
console.log('   或单独重启前端: docker-compose -f docker-compose.windows.yml restart editor');
console.log('');
console.log('2. 登录后应该不再自动显示"解决冲突"窗口');
console.log('');
console.log('3. 如需手动测试冲突功能，在浏览器控制台运行:');
console.log('   window.testConflictFunctions.createTestConflict()');
console.log('');
console.log('4. 冲突面板现在只在以下页面显示:');
console.log('   - /editor/ (编辑器页面)');
console.log('   - /terrain-editor (地形编辑器)');
console.log('   - /feedback-demo (反馈演示页面)');

if (allTestsPassed) {
  console.log('\n✅ 所有测试通过！冲突窗口自动显示问题已修复！');
  process.exit(0);
} else {
  console.log('\n❌ 部分测试失败，请检查修复内容');
  process.exit(1);
}
